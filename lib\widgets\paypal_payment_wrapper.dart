import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

class PayPalPaymentWrapper extends StatefulWidget {
  final bool sandboxMode;
  final String clientId;
  final String secretKey;
  final List<Map<String, dynamic>> transactions;
  final String note;

  const PayPalPaymentWrapper({
    Key? key,
    required this.sandboxMode,
    required this.clientId,
    required this.secretKey,
    required this.transactions,
    required this.note,
  }) : super(key: key);

  @override
  State<PayPalPaymentWrapper> createState() => _PayPalPaymentWrapperState();
}

class _PayPalPaymentWrapperState extends State<PayPalPaymentWrapper> {
  bool _hasReturned = false;
  late WebViewController _webViewController;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            print('PayPal page started loading: $url');
          },
          onPageFinished: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
            }
            print('PayPal page finished loading: $url');
            _checkForPaymentResult(url);
          },
          onNavigationRequest: (NavigationRequest request) {
            print('PayPal navigation request: ${request.url}');
            _checkForPaymentResult(request.url);
            return NavigationDecision.navigate;
          },
        ),
      );

    // Create PayPal payment and load URL
    _createPayPalPayment();
  }

  Future<void> _createPayPalPayment() async {
    try {
      // Tạo transaction data
      final transaction = widget.transactions.first;
      final amount = transaction['amount']['total'];
      final currency = transaction['amount']['currency'];

      print('Creating PayPal payment: $amount $currency');

      if (widget.sandboxMode) {
        // Trong sandbox mode, tạo demo payment với PayPal sandbox URL thực
        await _createSandboxPayment(amount, currency);
      } else {
        // Production mode - tạo PayPal payment thực qua API
        await _createProductionPayment(amount, currency);
      }
    } catch (e) {
      print('Error creating PayPal payment: $e');
      _handleError('Failed to create PayPal payment: $e');
    }
  }

  Future<void> _createSandboxPayment(String amount, String currency) async {
    try {
      print('🔑 PayPal Config Check:');
      print('  - Client ID: ${widget.clientId}');
      print('  - Secret Key: ${widget.secretKey.substring(0, 10)}...');
      print('  - Sandbox Mode: ${widget.sandboxMode}');

      // Tạo PayPal payment qua REST API
      print('🔄 Getting PayPal access token...');
      final accessToken = await _getPayPalAccessToken();
      if (accessToken == null) {
        throw Exception('Failed to get PayPal access token');
      }
      print('✅ Access token received: ${accessToken.substring(0, 20)}...');

      final paymentData = {
        'intent': 'sale',
        'payer': {'payment_method': 'paypal'},
        'transactions': [
          {
            'amount': {'total': amount, 'currency': currency},
            'description': 'Movie Ticket Payment - Đớp Phim App'
          }
        ],
        'redirect_urls': {
          'return_url': 'https://dopphim.app/paypal/success',
          'cancel_url': 'https://dopphim.app/paypal/cancel'
        }
      };

      final response = await http.post(
        Uri.parse('https://api.sandbox.paypal.com/v1/payments/payment'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: json.encode(paymentData),
      );

      if (response.statusCode == 201) {
        final responseData = json.decode(response.body);
        final approvalUrl = responseData['links']
            ?.firstWhere((link) => link['rel'] == 'approval_url')['href'];

        if (approvalUrl != null) {
          print('PayPal approval URL: $approvalUrl');
          _webViewController.loadRequest(Uri.parse(approvalUrl));
        } else {
          throw Exception('No approval URL found in PayPal response');
        }
      } else {
        throw Exception(
            'PayPal API error: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Sandbox payment error: $e');
      // Fallback to demo page if API fails
      await _loadDemoPage(amount, currency);
    }
  }

  Future<String?> _getPayPalAccessToken() async {
    try {
      final credentials =
          base64Encode(utf8.encode('${widget.clientId}:${widget.secretKey}'));
      print('🔐 Encoded credentials: ${credentials.substring(0, 20)}...');

      final url = 'https://api.sandbox.paypal.com/v1/oauth2/token';
      print('🌐 Making request to: $url');

      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Accept': 'application/json',
          'Accept-Language': 'en_US',
          'Authorization': 'Basic $credentials',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'grant_type=client_credentials',
      );

      print('📡 Response status: ${response.statusCode}');
      print('📡 Response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['access_token'];
      } else {
        print(
            '❌ Failed to get access token: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('❌ Access token error: $e');
      return null;
    }
  }

  Future<void> _createProductionPayment(String amount, String currency) async {
    // Production PayPal API implementation
    // Tương tự sandbox nhưng sử dụng production URLs
    throw UnimplementedError('Production PayPal not implemented yet');
  }

  Future<void> _loadDemoPage(String amount, String currency) async {
    // Fallback demo page nếu PayPal API không hoạt động
    final htmlContent = '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal Payment Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        .paypal-logo {
            font-size: 32px;
            font-weight: bold;
            color: #0070ba;
            background: white;
            padding: 10px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: inline-block;
        }
        .amount {
            font-size: 24px;
            margin: 20px 0;
            color: #ffd700;
        }
        .description {
            margin: 15px 0;
            opacity: 0.9;
        }
        .buttons {
            margin-top: 30px;
        }
        .btn {
            padding: 12px 30px;
            margin: 10px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        .btn-cancel {
            background: #dc3545;
            color: white;
        }
        .btn-cancel:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        .info {
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="paypal-logo">PayPal</div>
        <h2>Demo Payment (Fallback)</h2>
        <div class="amount">$amount $currency</div>
        <div class="description">Movie Ticket Payment<br>Đớp Phim App</div>

        <div class="buttons">
            <a href="javascript:void(0)" onclick="approvePayment()" class="btn btn-success">
                ✓ Approve Payment
            </a>
            <a href="javascript:void(0)" onclick="cancelPayment()" class="btn btn-cancel">
                ✗ Cancel Payment
            </a>
        </div>

        <div class="info">
            PayPal API fallback demo.<br>
            Click "Approve" to simulate successful payment.
        </div>
    </div>

    <script>
        function approvePayment() {
            window.location.href = 'https://dopphim.app/paypal/success?payment_id=DEMO_' + Date.now() + '&payer_id=DEMO_PAYER&token=DEMO_TOKEN';
        }

        function cancelPayment() {
            window.location.href = 'https://dopphim.app/paypal/cancel?token=DEMO_TOKEN';
        }
    </script>
</body>
</html>
    ''';

    final bytes = utf8.encode(htmlContent);
    final base64Html = base64Encode(bytes);
    _webViewController
        .loadRequest(Uri.parse('data:text/html;base64,$base64Html'));
  }

  void _checkForPaymentResult(String url) {
    if (_hasReturned) return;

    print('PayPal URL check: $url');

    // Kiểm tra URL để xác định kết quả thanh toán
    if (url.contains('dopphim.app/paypal/success') ||
        url.contains('demo.paypal.com/success') ||
        url.contains('paypal.com') && url.contains('success') ||
        url.contains('success') ||
        url.contains('approved')) {
      // Extract payment details from URL if available
      final uri = Uri.tryParse(url);
      String paymentId = 'PAYMENT_${DateTime.now().millisecondsSinceEpoch}';
      String payerId = 'PAYER_ID';
      String token = 'TOKEN';

      if (uri != null) {
        paymentId = uri.queryParameters['paymentId'] ??
            uri.queryParameters['payment_id'] ??
            uri.queryParameters['PayerID'] ??
            paymentId;
        payerId = uri.queryParameters['PayerID'] ??
            uri.queryParameters['payer_id'] ??
            payerId;
        token = uri.queryParameters['token'] ?? token;
      }

      // Nếu là PayPal sandbox thực, cần execute payment
      if (url.contains('paypal.com') &&
          !url.contains('demo') &&
          !url.contains('dopphim.app')) {
        _executePayPalPayment(paymentId, payerId, token);
      } else {
        // Demo payment hoặc đã execute rồi
        _handleSuccess({
          'error': false,
          'message': 'Success',
          'data': {
            'id': paymentId,
            'payer': {
              'payer_info': {'payer_id': payerId}
            }
          },
          'token': token
        });
      }
    } else if (url.contains('dopphim.app/paypal/cancel') ||
        url.contains('demo.paypal.com/cancel') ||
        url.contains('cancel') ||
        url.contains('cancelled')) {
      _handleCancel();
    } else if (url.contains('error') || url.contains('failed')) {
      _handleError('Payment failed');
    }
  }

  Future<void> _executePayPalPayment(
      String paymentId, String payerId, String token) async {
    try {
      print('Executing PayPal payment: $paymentId with payer: $payerId');

      final accessToken = await _getPayPalAccessToken();
      if (accessToken == null) {
        throw Exception('Failed to get PayPal access token for execution');
      }

      final executeData = {'payer_id': payerId};

      final response = await http.post(
        Uri.parse(
            'https://api.sandbox.paypal.com/v1/payments/payment/$paymentId/execute'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: json.encode(executeData),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        print('PayPal payment executed successfully: ${responseData['id']}');

        _handleSuccess({
          'error': false,
          'message': 'Success',
          'data': {
            'id': responseData['id'],
            'payer': {
              'payer_info': {'payer_id': payerId}
            }
          },
          'token': token,
          'paypal_response': responseData
        });
      } else {
        throw Exception(
            'PayPal execute error: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Execute payment error: $e');
      _handleError('Failed to execute PayPal payment: $e');
    }
  }

  void _handleSuccess(Map params) {
    if (!_hasReturned) {
      _hasReturned = true;
      print("PayPal payment success: $params");

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop(params);
        }
      });
    }
  }

  void _handleError(dynamic error) {
    if (!_hasReturned) {
      _hasReturned = true;
      print("PayPal payment error: $error");

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop({'error': error});
        }
      });
    }
  }

  void _handleCancel() {
    if (!_hasReturned) {
      _hasReturned = true;
      print("PayPal payment cancelled");

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop({'cancelled': true});
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (!didPop && !_hasReturned) {
          _hasReturned = true;
          Navigator.of(context).pop({'cancelled': true});
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('PayPal Payment'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              if (!_hasReturned) {
                _hasReturned = true;
                Navigator.of(context).pop({'cancelled': true});
              }
            },
          ),
          actions: [
            // Thêm nút demo success cho testing
            if (widget.sandboxMode)
              IconButton(
                icon: const Icon(Icons.check_circle),
                onPressed: () {
                  _handleSuccess({
                    'error': false,
                    'message': 'Success',
                    'data': {
                      'id':
                          'DEMO_PAYMENT_${DateTime.now().millisecondsSinceEpoch}',
                      'payer': {
                        'payer_info': {'payer_id': 'DEMO_PAYER_ID'}
                      }
                    },
                    'token': 'DEMO_TOKEN'
                  });
                },
                tooltip: 'Demo Success',
              ),
          ],
        ),
        body: Stack(
          children: [
            WebViewWidget(controller: _webViewController),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }
}
