import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';
import 'dart:io';

class PayPalPaymentWrapper extends StatefulWidget {
  final bool sandboxMode;
  final String clientId;
  final String secretKey;
  final List<Map<String, dynamic>> transactions;
  final String note;

  const PayPalPaymentWrapper({
    Key? key,
    required this.sandboxMode,
    required this.clientId,
    required this.secretKey,
    required this.transactions,
    required this.note,
  }) : super(key: key);

  @override
  State<PayPalPaymentWrapper> createState() => _PayPalPaymentWrapperState();
}

class _PayPalPaymentWrapperState extends State<PayPalPaymentWrapper> {
  bool _hasReturned = false;
  late WebViewController _webViewController;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            print('PayPal page started loading: $url');
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            print('PayPal page finished loading: $url');
            _checkForPaymentResult(url);
          },
          onNavigationRequest: (NavigationRequest request) {
            print('PayPal navigation request: ${request.url}');
            _checkForPaymentResult(request.url);
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(_buildPayPalUrl()));
  }

  String _buildPayPalUrl() {
    // Tạo URL PayPal sandbox hoặc production
    final baseUrl = widget.sandboxMode
        ? 'https://www.sandbox.paypal.com'
        : 'https://www.paypal.com';

    // Tạo transaction data
    final transaction = widget.transactions.first;
    final amount = transaction['amount']['total'];
    final currency = transaction['amount']['currency'];

    // Tạo URL đơn giản cho demo - trong thực tế cần tạo payment qua PayPal API
    return '$baseUrl/checkoutnow?token=demo_token&amount=$amount&currency=$currency';
  }

  void _checkForPaymentResult(String url) {
    if (_hasReturned) return;

    // Kiểm tra URL để xác định kết quả thanh toán
    if (url.contains('success') || url.contains('approved')) {
      _handleSuccess({
        'error': false,
        'message': 'Success',
        'data': {
          'id': 'DEMO_PAYMENT_${DateTime.now().millisecondsSinceEpoch}',
          'payer': {
            'payer_info': {'payer_id': 'DEMO_PAYER_ID'}
          }
        },
        'token': 'DEMO_TOKEN'
      });
    } else if (url.contains('cancel') || url.contains('cancelled')) {
      _handleCancel();
    } else if (url.contains('error') || url.contains('failed')) {
      _handleError('Payment failed');
    }
  }

  void _handleSuccess(Map params) {
    if (!_hasReturned) {
      _hasReturned = true;
      print("PayPal payment success: $params");

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop(params);
        }
      });
    }
  }

  void _handleError(dynamic error) {
    if (!_hasReturned) {
      _hasReturned = true;
      print("PayPal payment error: $error");

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop({'error': error});
        }
      });
    }
  }

  void _handleCancel() {
    if (!_hasReturned) {
      _hasReturned = true;
      print("PayPal payment cancelled");

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop({'cancelled': true});
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (!didPop && !_hasReturned) {
          _hasReturned = true;
          Navigator.of(context).pop({'cancelled': true});
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('PayPal Payment'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              if (!_hasReturned) {
                _hasReturned = true;
                Navigator.of(context).pop({'cancelled': true});
              }
            },
          ),
          actions: [
            // Thêm nút demo success cho testing
            if (widget.sandboxMode)
              IconButton(
                icon: const Icon(Icons.check_circle),
                onPressed: () {
                  _handleSuccess({
                    'error': false,
                    'message': 'Success',
                    'data': {
                      'id':
                          'DEMO_PAYMENT_${DateTime.now().millisecondsSinceEpoch}',
                      'payer': {
                        'payer_info': {'payer_id': 'DEMO_PAYER_ID'}
                      }
                    },
                    'token': 'DEMO_TOKEN'
                  });
                },
                tooltip: 'Demo Success',
              ),
          ],
        ),
        body: Stack(
          children: [
            WebViewWidget(controller: _webViewController),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }
}
