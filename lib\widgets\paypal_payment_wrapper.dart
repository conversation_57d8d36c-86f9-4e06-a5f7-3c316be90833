import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';

class PayPalPaymentWrapper extends StatefulWidget {
  final bool sandboxMode;
  final String clientId;
  final String secretKey;
  final List<Map<String, dynamic>> transactions;
  final String note;

  const PayPalPaymentWrapper({
    Key? key,
    required this.sandboxMode,
    required this.clientId,
    required this.secretKey,
    required this.transactions,
    required this.note,
  }) : super(key: key);

  @override
  State<PayPalPaymentWrapper> createState() => _PayPalPaymentWrapperState();
}

class _PayPalPaymentWrapperState extends State<PayPalPaymentWrapper> {
  bool _hasReturned = false;
  late WebViewController _webViewController;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            print('PayPal page started loading: $url');
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            print('PayPal page finished loading: $url');
            _checkForPaymentResult(url);
          },
          onNavigationRequest: (NavigationRequest request) {
            print('PayPal navigation request: ${request.url}');
            _checkForPaymentResult(request.url);
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(_buildPayPalUrl()));
  }

  String _buildPayPalUrl() {
    // Tạo transaction data
    final transaction = widget.transactions.first;
    final amount = transaction['amount']['total'];
    final currency = transaction['amount']['currency'];

    // Tạo HTML demo page cho PayPal payment
    final htmlContent = '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal Payment Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        .paypal-logo {
            font-size: 32px;
            font-weight: bold;
            color: #0070ba;
            background: white;
            padding: 10px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: inline-block;
        }
        .amount {
            font-size: 24px;
            margin: 20px 0;
            color: #ffd700;
        }
        .description {
            margin: 15px 0;
            opacity: 0.9;
        }
        .buttons {
            margin-top: 30px;
        }
        .btn {
            padding: 12px 30px;
            margin: 10px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        .btn-cancel {
            background: #dc3545;
            color: white;
        }
        .btn-cancel:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        .info {
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="paypal-logo">PayPal</div>
        <h2>Demo Payment</h2>
        <div class="amount">$amount $currency</div>
        <div class="description">Movie Ticket Payment<br>Đớp Phim App</div>

        <div class="buttons">
            <a href="javascript:void(0)" onclick="approvePayment()" class="btn btn-success">
                ✓ Approve Payment
            </a>
            <a href="javascript:void(0)" onclick="cancelPayment()" class="btn btn-cancel">
                ✗ Cancel Payment
            </a>
        </div>

        <div class="info">
            This is a demo PayPal payment page.<br>
            Click "Approve" to simulate successful payment.
        </div>
    </div>

    <script>
        function approvePayment() {
            // Simulate successful payment
            window.location.href = 'https://demo.paypal.com/success?payment_id=DEMO_' + Date.now() + '&payer_id=DEMO_PAYER&token=DEMO_TOKEN';
        }

        function cancelPayment() {
            // Simulate cancelled payment
            window.location.href = 'https://demo.paypal.com/cancel?token=DEMO_TOKEN';
        }

        // Auto-redirect after 30 seconds if no action
        setTimeout(function() {
            document.body.innerHTML += '<div style="position: fixed; top: 10px; right: 10px; background: rgba(255,0,0,0.8); padding: 10px; border-radius: 5px;">Auto-cancelling in 5 seconds...</div>';
            setTimeout(cancelPayment, 5000);
        }, 25000);
    </script>
</body>
</html>
    ''';

    // Encode HTML content as base64 data URL to avoid character encoding issues
    final bytes = utf8.encode(htmlContent);
    final base64Html = base64Encode(bytes);
    return 'data:text/html;base64,$base64Html';
  }

  void _checkForPaymentResult(String url) {
    if (_hasReturned) return;

    print('PayPal URL check: $url');

    // Kiểm tra URL để xác định kết quả thanh toán
    if (url.contains('demo.paypal.com/success') ||
        url.contains('success') ||
        url.contains('approved')) {
      // Extract payment details from URL if available
      final uri = Uri.tryParse(url);
      String paymentId =
          'DEMO_PAYMENT_${DateTime.now().millisecondsSinceEpoch}';
      String payerId = 'DEMO_PAYER_ID';
      String token = 'DEMO_TOKEN';

      if (uri != null) {
        paymentId = uri.queryParameters['payment_id'] ?? paymentId;
        payerId = uri.queryParameters['payer_id'] ?? payerId;
        token = uri.queryParameters['token'] ?? token;
      }

      _handleSuccess({
        'error': false,
        'message': 'Success',
        'data': {
          'id': paymentId,
          'payer': {
            'payer_info': {'payer_id': payerId}
          }
        },
        'token': token
      });
    } else if (url.contains('demo.paypal.com/cancel') ||
        url.contains('cancel') ||
        url.contains('cancelled')) {
      _handleCancel();
    } else if (url.contains('error') || url.contains('failed')) {
      _handleError('Payment failed');
    }
  }

  void _handleSuccess(Map params) {
    if (!_hasReturned) {
      _hasReturned = true;
      print("PayPal payment success: $params");

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop(params);
        }
      });
    }
  }

  void _handleError(dynamic error) {
    if (!_hasReturned) {
      _hasReturned = true;
      print("PayPal payment error: $error");

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop({'error': error});
        }
      });
    }
  }

  void _handleCancel() {
    if (!_hasReturned) {
      _hasReturned = true;
      print("PayPal payment cancelled");

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop({'cancelled': true});
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (!didPop && !_hasReturned) {
          _hasReturned = true;
          Navigator.of(context).pop({'cancelled': true});
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('PayPal Payment'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              if (!_hasReturned) {
                _hasReturned = true;
                Navigator.of(context).pop({'cancelled': true});
              }
            },
          ),
          actions: [
            // Thêm nút demo success cho testing
            if (widget.sandboxMode)
              IconButton(
                icon: const Icon(Icons.check_circle),
                onPressed: () {
                  _handleSuccess({
                    'error': false,
                    'message': 'Success',
                    'data': {
                      'id':
                          'DEMO_PAYMENT_${DateTime.now().millisecondsSinceEpoch}',
                      'payer': {
                        'payer_info': {'payer_id': 'DEMO_PAYER_ID'}
                      }
                    },
                    'token': 'DEMO_TOKEN'
                  });
                },
                tooltip: 'Demo Success',
              ),
          ],
        ),
        body: Stack(
          children: [
            WebViewWidget(controller: _webViewController),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }
}
